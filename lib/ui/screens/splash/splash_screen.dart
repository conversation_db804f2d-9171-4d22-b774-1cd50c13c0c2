import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/utils/utils.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  bool _showAnimatedLogo = false;
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final configVM = context.read<ConfigVM>();
      getPackageInfo();
      determinePosition();

      // Initialize FCM token service and set up refresh listener
      final fcmTokenService = FCMTokenService();
      fcmTokenService.getToken();
      fcmTokenService.setupTokenRefreshListener();

      // HeaderService().getDeviceInfo();

      context.read<AuthUserVM>().getUserFromStorage();
      configVM.getConfigurations().then((value) {
        printty(
            'Configurations BE: ${configVM.configData?.androidAppVersion}'); // Both for IOS and Android
        printty('Configurations Splash: ${configVM.appIsDueForUpdate}');
      });
    });

    _startLogoAnimation();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _fadeAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_animationController);

    _animationController.forward();

    Future.delayed(const Duration(seconds: 4), () {
      if (!mounted) return;

      if (context.read<ConfigVM>().appIsDueForUpdate) {
        Navigator.pushNamed(context, RoutePath.updateAvailableScreen);
        return;
      }

      Navigator.of(context).pushReplacementNamed(
        context.read<AuthUserVM>().user != null
            ? RoutePath.emailAndPasswordScreen
            : RoutePath.emailAndPasswordScreen,
      );
    });
  }

  getPackageInfo() {
    AppInitService().packageInfoInit().then((value) {
      if (value.isNotEmpty && mounted) {
        _appVersion = value;
        context.read<ConfigVM>().setMyAppCurrentVersion(value);
        setState(() {});
      }
    });
  }

  _startLogoAnimation() {
    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        _showAnimatedLogo = true;
      });
    });
  }

  @override
  void deactivate() {
    _animationController.dispose();
    super.deactivate();
  }

  @override
  Widget build(BuildContext context) {
    printty(
        'App appIsDueForUpdate: ${context.read<ConfigVM>().appIsDueForUpdate}');
    return Scaffold(
      backgroundColor: AppColors.blue200,
      body: Stack(
        children: [
          // Lottie.asset(
          //   AppGifs.clockwise,
          //   height: Sizer.height(180),
          // ),
          Center(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_showAnimatedLogo) const LogoLoader(),
                        if (!_showAnimatedLogo)
                          svgHelper(
                            AppSvgs.logo,
                            height: Sizer.height(50),
                            width: Sizer.width(50),
                          ),
                        const XBox(8),
                        Stack(
                          clipBehavior: Clip.none,
                          children: [
                            SizedBox(
                              height: Sizer.height(30),
                              width: Sizer.width(140),
                              child: svgHelper(
                                AppSvgs.korrency,
                                height: Sizer.height(30),
                                width: Sizer.width(140),
                              ),
                            ),
                            // Positioned(
                            //   top: -50,
                            //   right: 10,
                            //   child: svgHelper(
                            //     AppSvgs.splashIcon,
                            //   ),
                            // ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.center,
                    child: Text(
                      'Version $_appVersion',
                      style: AppTypography.text14.copyWith(
                        color: AppColors.grayDO,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const YBox(60),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
